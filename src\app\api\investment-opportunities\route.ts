import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const limit = parseInt(searchParams.get('limit') || '10')
    const page = parseInt(searchParams.get('page') || '1')
    const sort = searchParams.get('sort') || '-publishDate'
    const featured = searchParams.get('featured')
    const sector = searchParams.get('sector')
    const investmentType = searchParams.get('investmentType')
    const status = searchParams.get('status')
    const urgent = searchParams.get('urgent')

    // Build where clause
    const where: any = {}

    if (featured !== null) {
      where.featured = { equals: featured === 'true' }
    }

    if (sector) {
      where.sector = { equals: sector }
    }

    if (investmentType) {
      where.investmentType = { equals: investmentType }
    }

    if (status) {
      where.status = { equals: status }
    } else {
      // Default to active opportunities only
      where.status = { equals: 'active' }
    }

    if (urgent !== null) {
      where.urgent = { equals: urgent === 'true' }
    }

    // Fetch investment opportunities from CMS
    const result = await payload.find({
      collection: 'investment-opportunities',
      where,
      limit,
      page,
      sort: [sort],
      select: {
        id: true,
        title: true,
        summary: true,
        description: true,
        sector: true,
        investmentType: true,
        status: true,
        featured: true,
        urgent: true,
        funding: true,
        timeline: true,
        location: true,
        publishDate: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    return NextResponse.json({
      success: true,
      opportunities: result.docs,
      pagination: {
        totalDocs: result.totalDocs,
        limit: result.limit,
        totalPages: result.totalPages,
        page: result.page,
        pagingCounter: result.pagingCounter,
        hasPrevPage: result.hasPrevPage,
        hasNextPage: result.hasNextPage,
        prevPage: result.prevPage,
        nextPage: result.nextPage,
      },
    })
  } catch (error) {
    console.error('Investment Opportunities API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch investment opportunities',
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Handle both JSON and form data with proper error handling
    let body
    const contentType = request.headers.get('content-type') || ''

    try {
      if (contentType.includes('multipart/form-data')) {
        // Handle multipart form data (from Payload CMS admin)
        const formData = await request.formData()
        const payloadData = formData.get('_payload')

        if (payloadData && typeof payloadData === 'string') {
          body = JSON.parse(payloadData)
        } else {
          return NextResponse.json(
            {
              success: false,
              error: 'Invalid form data',
              message: 'Missing _payload field in form data',
            },
            { status: 400 }
          )
        }
      } else {
        // Handle regular JSON
        const rawBody = await request.text()
        if (!rawBody.trim()) {
          return NextResponse.json(
            {
              success: false,
              error: 'Empty request body',
              message: 'Request body cannot be empty',
            },
            { status: 400 }
          )
        }
        body = JSON.parse(rawBody)
      }
    } catch (parseError) {
      console.error('JSON parsing error:', parseError)
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid JSON',
          message: 'Request body must be valid JSON or form data',
          details: parseError instanceof Error ? parseError.message : 'Unknown parsing error',
        },
        { status: 400 }
      )
    }

    // Validate required fields
    if (!body || typeof body !== 'object') {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid data',
          message: 'Request body must be a valid object',
        },
        { status: 400 }
      )
    }

    // Create new investment opportunity
    const result = await payload.create({
      collection: 'investment-opportunities',
      data: body,
    })

    return NextResponse.json({
      success: true,
      opportunity: result,
      message: 'Investment opportunity created successfully',
    })
  } catch (error) {
    console.error('Investment Opportunities POST API error:', error)

    // Check if it's a validation error
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          message: error.message,
          details: error.data || error.details || null,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to create investment opportunity',
      },
      { status: 500 }
    )
  }
}
