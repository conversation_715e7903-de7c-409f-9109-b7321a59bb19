'use client'

import React, { useState, useEffect } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPICard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import { Calendar, MapPin, ExternalLink, Filter } from 'lucide-react'

interface Event {
  id: string
  title: string
  description: string
  date: string
  endDate?: string
  time: string
  location: string
  venue: string
  type: 'conference' | 'workshop' | 'webinar' | 'training' | 'exhibition' | 'meeting'
  category: string
  capacity: number
  registered: number
  price: string
  organizer: string
  tags: string[]
  registrationUrl?: string
  featured?: boolean
}

interface NPIEventsCalendarProps {
  title?: string
  description?: string
  events?: Event[]
}

export const NPIEventsCalendarBlock: React.FC<NPIEventsCalendarProps> = ({
  title = 'Upcoming Events',
  description = 'Join us at conferences, workshops, and training sessions designed to advance natural products development and share knowledge across Kenya.',
}) => {
  const [events, setEvents] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedType, setSelectedType] = useState('All Types')
  const [selectedCategory, setSelectedCategory] = useState('All Categories')

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch('/api/events?limit=10&sort=schedule.startDate')

        if (!response.ok) {
          throw new Error(`Failed to fetch events: ${response.status}`)
        }

        const data = await response.json()

        if (data.success && data.events) {
          setEvents(data.events)
        } else {
          throw new Error('Invalid response format')
        }
      } catch (err) {
        console.error('Error fetching events:', err)
        setError(err instanceof Error ? err.message : 'Failed to load events')
        // Fallback to empty array
        setEvents([])
      } finally {
        setLoading(false)
      }
    }

    fetchEvents()
  }, [])

  // Helper functions for CMS data
  const getEventLocation = (event: any): string => {
    if (event.location?.venue) {
      return event.location.venue
    }
    if (event.location?.address) {
      return event.location.address
    }
    if (event.venue) {
      return event.venue
    }
    return 'TBD'
  }

  if (loading) {
    return (
      <NPISection className="bg-white">
        <NPISectionHeader>
          <NPISectionTitle className="text-black">{title}</NPISectionTitle>
          <NPISectionDescription className="text-[#725242]">{description}</NPISectionDescription>
        </NPISectionHeader>
        <div className="flex justify-center items-center py-12">
          <div className="text-[#725242]">Loading events...</div>
        </div>
      </NPISection>
    )
  }

  if (error) {
    return (
      <NPISection className="bg-white">
        <NPISectionHeader>
          <NPISectionTitle className="text-black">{title}</NPISectionTitle>
          <NPISectionDescription className="text-[#725242]">{description}</NPISectionDescription>
        </NPISectionHeader>
        <div className="flex justify-center items-center py-12">
          <div className="text-red-600">Error: {error}</div>
        </div>
      </NPISection>
    )
  }

  const types = ['All Types', ...Array.from(new Set(events.map((e) => e.type || 'General')))]
  const categories = ['All Categories', ...Array.from(new Set(events.map((e) => e.category || 'General')))]

  const filteredEvents = events.filter((event) => {
    const eventType = event.type || 'General'
    const eventCategory = event.category || 'General'

    return (
      (selectedType === 'All Types' || eventType === selectedType) &&
      (selectedCategory === 'All Categories' || eventCategory === selectedCategory)
    )
  })

  const featuredEvents = filteredEvents.filter((e) => e.featured)
  const regularEvents = filteredEvents.filter((e) => !e.featured)

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'conference':
        return 'bg-[#8A3E25] text-[#FFFFFF]'
      case 'workshop':
        return 'bg-[#725242] text-[#FFFFFF]'
      case 'webinar':
        return 'bg-[#25718A] text-[#FFFFFF]'
      case 'training':
        return 'bg-[#8A3E25] text-[#FFFFFF]'
      case 'exhibition':
        return 'bg-[#725242] text-[#FFFFFF]'
      case 'meeting':
        return 'bg-[#EFE3BA] text-black'
      default:
        return 'bg-[#EFE3BA] text-black'
    }
  }

  const formatDate = (dateString: string, endDateString?: string) => {
    const startDate = new Date(dateString)
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }

    if (endDateString) {
      const endDate = new Date(endDateString)
      if (startDate.getMonth() === endDate.getMonth()) {
        return `${startDate.getDate()}-${endDate.getDate()} ${startDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}`
      } else {
        return `${startDate.toLocaleDateString('en-US', options)} - ${endDate.toLocaleDateString('en-US', options)}`
      }
    }

    return startDate.toLocaleDateString('en-US', options)
  }

  return (
    <NPISection id="events">
      <NPISectionHeader>
        <NPISectionTitle className="text-[#2F2C29]">{title}</NPISectionTitle>
        <NPISectionDescription className="text-[#46372A]">{description}</NPISectionDescription>
        <div className="w-24 h-1 bg-gradient-to-r from-[#A7795E] via-[#8A6240] to-[#6E3C19] mx-auto mt-4"></div>
      </NPISectionHeader>

      {/* Filters */}
      <NPICard className="mb-8 rounded-none border-l-4 border-[#A7795E]">
        <NPICardContent className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <Filter className="w-5 h-5 text-[#6E3C19]" />
            <span className="font-medium font-npi text-[#25718A]">Filter Events:</span>
          {/* Ensure this closing tag matches an opened <div> above, or remove if unnecessary */}
          </div>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2 font-npi text-[#46372A]">
                Event Type
              </label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full p-2 border border-[#CEC9BC] focus:outline-none focus:ring-2 focus:ring-[#A7795E] font-npi bg-white text-[#2F2C29]"
              >
                {types.map((type) => (
                  <option key={type} value={type}>
                    {type === 'All Types' ? type : type.charAt(0).toUpperCase() + type.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 font-npi text-[#46372A]">
                Category
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full p-2 border border-[#CEC9BC] focus:outline-none focus:ring-2 focus:ring-[#A7795E] font-npi bg-white text-[#2F2C29]"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </NPICardContent>
      </NPICard>

      {/* Featured Events */}
      {featuredEvents.length > 0 && (
        <div className="mb-12">
          <div className="flex items-center gap-4 mb-6">
            <h3 className="text-2xl font-bold font-npi text-black">Featured Events</h3>
            <div className="flex-1 h-0.5 bg-[#725242]"></div>
          </div>
          <div className="grid lg:grid-cols-4 gap-6">
            {featuredEvents.slice(0, 4).map((event, index) => (
              <NPICard
                key={event.id}
                className={`hover:shadow-lg transition-all duration-300 aspect-square flex flex-col border-2${
                  index % 2 === 0
                    ? 'bg-white border-[#8A3E25] hover:border-[#25718A]'
                    : 'bg-white border-[#725242] hover:border-[#8A3E25]'
                }`}
              >
                <NPICardHeader className="flex-shrink-0">
                  <div className="flex items-center justify-between mb-3">
                    <span className={`px-3 py-1 text-sm font-medium ${getTypeColor(event.type || 'general')}`}>
                      {(event.type || 'general').charAt(0).toUpperCase() + (event.type || 'general').slice(1)}
                    </span>
                    <span className="bg-[#8A3E25] text-white px-2 py-1 text-xs font-medium">
                      Featured
                    </span>
                  </div>
                  <NPICardTitle className="text-lg leading-tight text-black line-clamp-2">{event.title}</NPICardTitle>
                  <div className="text-sm text-[#725242] font-medium font-npi">{event.category || 'General'}</div>
                </NPICardHeader>

                <NPICardContent className="flex-grow flex flex-col">
                  <div className="space-y-2 text-xs mb-4 flex-grow">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-3 h-3 text-[#725242]" />
                      <span className="font-npi text-[#725242]">
                        {event.schedule?.startDate ?
                          formatDate(event.schedule.startDate, event.schedule?.endDate) :
                          'TBD'
                        }
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-3 h-3 text-[#725242]" />
                      <span className="font-npi text-[#725242]">{getEventLocation(event)}</span>
                    </div>
                  </div>
                </NPICardContent>

                <NPICardFooter className="flex gap-2 flex-shrink-0">
                  {event.registration?.required ? (
                    <NPIButton
                      asChild
                      variant="primary"
                      className="flex-1 bg-[#25718A] hover:bg-[#25718A] text-white"
                      size="sm"
                    >
                      <Link href={`/events/register/${event.slug || event.id}`}>Register</Link>
                    </NPIButton>
                  ) : (
                    <NPIButton
                      variant="outline"
                      className="flex-1 border-[#725242] text-[#725242]"
                      size="sm"
                      disabled
                    >
                      No Registration
                    </NPIButton>
                  )}
                  <NPIButton
                    asChild
                    variant="outline"
                    size="sm"
                    className="border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white"
                  >
                    <Link href={`/events/${event.slug || event.id}`}>
                      <ExternalLink className="w-3 h-3" />
                    </Link>
                  </NPIButton>
                </NPICardFooter>
              </NPICard>
            ))}
          </div>
        </div>
      )}

      {/* Regular Events */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-6">
          <h3 className="text-2xl font-bold font-npi text-black">All Events</h3>
          <div className="flex-1 h-0.5 bg-[#725242]"></div>
        </div>
      </div>
      <div className="grid lg:grid-cols-4 gap-6">
        {regularEvents.map((event, index) => (
          <NPICard
            key={event.id}
            className={`hover:shadow-lg transition-all duration-300 aspect-square flex flex-col border-2 ${
              index % 2 === 0
                ? 'bg-white border-[#725242] hover:border-[#8A3E25]'
                : 'bg-white border-[#8A3E25] hover:border-[#25718A]'
            }`}
          >
            <NPICardHeader className="flex-shrink-0">
              <div className="flex items-center justify-between mb-2">
                <span className={`px-2 py-1 text-xs font-medium ${getTypeColor(event.type || 'general')}`}>
                  {(event.type || 'general').charAt(0).toUpperCase() + (event.type || 'general').slice(1)}
                </span>
                <span className="text-xs text-[#725242] font-npi">{event.category || 'General'}</span>
              </div>
              <NPICardTitle className="text-lg leading-tight text-black line-clamp-2">{event.title}</NPICardTitle>
            </NPICardHeader>

            <NPICardContent className="flex-grow flex flex-col">
              <div className="space-y-2 text-xs mb-4 flex-grow">
                <div className="flex items-center gap-2">
                  <Calendar className="w-3 h-3 text-[#725242]" />
                  <span className="font-npi text-[#725242]">
                    {event.schedule?.startDate ?
                      formatDate(event.schedule.startDate, event.schedule?.endDate) :
                      'TBD'
                    }
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-3 h-3 text-[#725242]" />
                  <span className="font-npi text-[#725242]">{getEventLocation(event)}</span>
                </div>
              </div>
            </NPICardContent>

            <NPICardFooter className="flex gap-2 flex-shrink-0">
              {event.registration?.required ? (
                <NPIButton
                  asChild
                  variant="outline"
                  className="flex-1 border-[#8A3E25] text-[#25718A] hover:bg-[#8A3E25] hover:text-white"
                  size="sm"
                >
                  <Link href={`/events/register/${event.slug || event.id}`}>Register</Link>
                </NPIButton>
              ) : (
                <NPIButton
                  variant="ghost"
                  className="flex-1 text-[#725242]"
                  size="sm"
                  disabled
                >
                  No Registration
                </NPIButton>
              )}
            </NPICardFooter>
          </NPICard>
        ))}
      </div>

      {/* Results Summary */}
      <div className="text-center mt-8">
        <p className="text-[#725242] font-npi">
          Showing <span className="font-bold text-[#25718A]">{filteredEvents.length}</span> of{' '}
          <span className="font-bold text-[#25718A]">{events.length}</span> events
        </p>
      </div>
    </NPISection>
  )
}
